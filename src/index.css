@import url("https://fonts.googleapis.com/css2?family=Funnel+Display:wght@300..800&display=swap");
@import "tailwindcss";
@theme {
  --color-primary: #030412;
  --color-midnight: #06091f;
  --color-navy: #161a31;
  --color-indigo: #1f1e39;
  --color-storm: #282b4b;
  --color-aqua: #33c2cc;
  --color-mint: #57db96;
  --color-royal: #5c33cc;
  --color-lavender: #7a57db;
  --color-fuchsia: #ca2f8c;
  --color-orange: #cc6033;
  --color-sand: #d6995c;
  --color-coral: #ea4884;
  --animate-orbit: orbit 50s linear infinite;
  @keyframes orbit {
    0% {
      transform: rotate(calc(var(--angle) * 1deg))
        translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg));
    }
    100% {
      transform: rotate(calc(var(--angle) * 1deg + 360deg))
        translateY(calc(var(--radius) * 1px))
        rotate(calc((var(--angle) * -1deg) - 360deg));
    }
  }
  --animate-marquee: marquee 50s linear infinite;
  --animate-marquee-vertical: marquee-vertical 50s linear infinite;

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
}
body {
  background: #030412;
  font-family: "Funnel Display", sans-serif;
  color: white;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

.c-space {
  @apply sm:px-10 px-5 lg:px-15;
}

.hover-animation {
  @apply hover:-translate-y-1 duration-200;
}

.section-spacing {
  @apply min-h-screen mt-20 md:mt-30;
}

.text-heading {
  @apply font-bold text-3xl md:text-4xl;
}

.headtext {
  @apply mt-2 mb-2 text-xl;
}

.subtext {
  @apply text-neutral-400 text-sm md:text-base text-pretty;
}

/* Navigation */
.nav-ul {
  @apply flex flex-col items-center gap-4 sm:flex-row md:gap-6 relative z-20;
}

.nav-li {
  @apply text-neutral-400 hover:text-white max-sm:w-full max-sm:rounded-md py-2 max-sm:px-5;
}

.nav-link {
  @apply text-lg md:text-base hover:text-white transition-colors;
}
/* Button Components */
.btn {
  @apply relative px-1 py-4 text-sm text-center rounded-full font-extralight bg-primary w-[12rem] cursor-pointer overflow-hidden;
}

/* About Section */
.grid-1 {
  @apply row-span-2 md:col-span-3 h-[15rem] md:h-full relative overflow-hidden hover:-translate-y-1 duration-200;
}
.grid-2 {
  @apply row-span-1 md:col-span-3 h-[15rem] md:h-full relative overflow-hidden hover:-translate-y-1 duration-200;
}
.grid-3 {
  @apply row-span-1 md:col-span-3 h-[15rem] md:h-full relative overflow-hidden hover:-translate-y-1 duration-200;
}
.grid-4 {
  @apply row-span-1 md:col-span-2 h-[15rem] md:h-full relative overflow-hidden hover:-translate-y-1 duration-200;
}
.grid-5 {
  @apply row-span-1 md:col-span-4 h-[15rem] md:h-full relative overflow-hidden hover:-translate-y-1 duration-200;
}

.grid-default-color {
  @apply p-6 bg-gradient-to-b from-storm to-indigo rounded-2xl;
}

.grid-special-color {
  @apply p-6 bg-gradient-to-b from-royal to-lavender rounded-2xl;
}
.grid-black-color {
  @apply p-6 bg-gradient-to-tl from-[#3A3A3A] via-[#242424] to-[#3A3A3A] rounded-2xl;
}

/* Contact Section*/
.field-label {
  @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
}

.field-input {
  @apply w-full min-h-10 rounded-md px-3 py-2 text-sm bg-white/10 transition duration-200 placeholder-neutral-500 border border-white/10 mt-2;
}

.field-input-focus {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/20;
}
