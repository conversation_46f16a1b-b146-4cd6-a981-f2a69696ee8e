#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/jiti@2.4.2/node_modules:/home/<USER>/Workspace/Frontend/portfolio/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../jiti@2.4.2/node_modules/jiti/lib/jiti-cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../jiti@2.4.2/node_modules/jiti/lib/jiti-cli.mjs" "$@"
fi
