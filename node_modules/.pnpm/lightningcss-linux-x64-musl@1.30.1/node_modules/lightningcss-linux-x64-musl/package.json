{"name": "lightningcss-linux-x64-musl", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.linux-x64-musl.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "files": ["lightningcss.linux-x64-musl.node"], "resolutions": {"lightningcss": "link:."}, "os": ["linux"], "cpu": ["x64"], "libc": ["musl"]}